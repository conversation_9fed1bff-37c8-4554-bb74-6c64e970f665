# Base image with <PERSON><PERSON> installed (same as cron service)
FROM mcr.microsoft.com/playwright:v1.42.1-jammy

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Install tsx globally for running TypeScript files directly
RUN npm install -g tsx

# Set environment variables
ENV NODE_ENV=production
ENV TZ=America/New_York

# Create a health check script
RUN echo '#!/bin/bash\necho "Scraper service is healthy"' > /app/health.sh && chmod +x /app/health.sh

# Start the scraper service
CMD ["npm", "start"]
